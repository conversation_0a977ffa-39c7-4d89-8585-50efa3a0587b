"use client"

import React, { useState, useEffect } from 'react'
import { Plus, Package, Key } from 'lucide-react'
import { useAdminCRUD } from '../hooks/useAdminCRUD'
import { useAdminModal } from '../hooks/useAdminModal'
import AdminTable, { ColumnDefinition } from './shared/AdminTable'
import AdminPagination from './shared/AdminPagination'
import AdminFilters, { FilterOption } from './shared/AdminFilters'
import AdminModal from './shared/AdminModal'
import ProductForm from './ProductForm'
import { useTenant } from '../../contexts/TenantContext'
import { getCategories } from '../../lib/categories'
import { 
  Product, 
  SimplifiedCustomField, 
  Category,
  getInitialFormData,
  getInitialCustomFields
} from '../utils/product-utils'

export default function ProductManagement() {
  const { tenant } = useTenant()
  const [categories, setCategories] = useState<Category[]>([])
  const [categoriesLoading, setCategoriesLoading] = useState(false)
  const [formData, setFormData] = useState<Partial<Product>>(getInitialFormData())
  const [customFields, setCustomFields] = useState<SimplifiedCustomField[]>(getInitialCustomFields())

  // CRUD operations
  const {
    items: products,
    loading,
    actionLoading,
    pagination,
    filters,
    fetchItems,
    createItem,
    updateItem,
    deleteItem,
    goToPage,
    nextPage,
    prevPage,
    updateFilters
  } = useAdminCRUD<Product>({
    entityType: 'products',
    apiEndpoint: '/api/admin/products',
    cacheKey: 'admin-products'
  })

  // Modal management
  const {
    isOpen: isModalOpen,
    mode: modalMode,
    item: editingProduct,
    openCreateModal,
    openEditModal,
    closeModal
  } = useAdminModal<Product>()

  // Load categories on mount
  useEffect(() => {
    const loadCategories = async () => {
      if (!tenant?.id) return

      setCategoriesLoading(true)
      try {
        const result = await getCategories(tenant.id)
        if (result.success && result.data) {
          setCategories(result.data)
        } else {
          console.error('Failed to load categories:', result.error)
          setCategories([])
        }
      } catch (error) {
        console.error('Error loading categories:', error)
        setCategories([])
      } finally {
        setCategoriesLoading(false)
      }
    }

    loadCategories()
  }, [tenant?.id])

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isModalOpen) {
      if (modalMode === 'edit' && editingProduct) {
        setFormData({
          title: editingProduct.title,
          description: editingProduct.description,
          category_id: editingProduct.category_id,
          tags: editingProduct.tags || [],
          cover_image: editingProduct.cover_image,
          packages: editingProduct.packages || [],
          featured: editingProduct.featured,
          // Product-level pricing
          original_price: editingProduct.original_price,
          user_price: editingProduct.user_price,
          discount_price: editingProduct.discount_price,
          distributor_price: editingProduct.distributor_price
        })

        // Load existing custom fields
        setCustomFields(editingProduct.customFields || [])
      } else {
        setFormData(getInitialFormData())
        setCustomFields(getInitialCustomFields())
      }
    }
  }, [isModalOpen, modalMode, editingProduct])

  // Table columns definition
  const columns: ColumnDefinition<Product>[] = [
    {
      key: 'title',
      label: 'المنتج',
      render: (product) => (
        <div className="flex items-center space-x-3 space-x-reverse">
          <img
            src={product.cover_image || "/logo.jpg"}
            alt={product.title}
            className="w-12 h-12 rounded-lg object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement
              target.src = "/logo.jpg"
            }}
          />
          <div>
            <p className="font-semibold">{product.title}</p>
            <p className="text-sm text-gray-400">{product.slug}</p>
          </div>
        </div>
      )
    },
    {
      key: 'category',
      label: 'الفئة',
      render: (product) => product.categories?.name || 'غير محدد'
    },
    {
      key: 'packages',
      label: 'الحزم',
      render: (product) => `${product.packages?.length || 0} حزمة`
    },
    {
      key: 'digital_codes',
      label: 'الأكواد الرقمية',
      render: (product) => {
        const totalCodes = product.packages?.reduce((sum, pkg) => sum + (pkg.digitalCodes?.length || 0), 0) || 0
        const availableCodes = product.packages?.reduce((sum, pkg) => sum + (pkg.availableCodesCount || 0), 0) || 0
        const hasDigitalPackages = product.packages?.some(pkg => pkg.hasDigitalCodes || pkg.has_digital_codes)

        if (!hasDigitalPackages) {
          return <span className="text-gray-500 text-sm">لا يوجد</span>
        }

        return (
          <div className="flex items-center space-x-1 space-x-reverse">
            <Key className="w-3 h-3 text-blue-400" />
            <span className="text-xs">{availableCodes} كود</span>
          </div>
        )
      }
    },
    {
      key: 'inventory',
      label: 'المخزون',
      render: (product) => {
        const manualInventoryPackages = product.packages?.filter(pkg => pkg.track_inventory) || []
        const digitalPackages = product.packages?.filter(pkg => pkg.hasDigitalCodes || pkg.has_digital_codes) || []

        if (manualInventoryPackages.length === 0 && digitalPackages.length === 0) {
          return <span className="text-gray-500 text-sm">غير محدد</span>
        }

        const inventoryItems = []

        // Show manual inventory status
        if (manualInventoryPackages.length > 0) {
          const totalManualStock = manualInventoryPackages.reduce((sum, pkg) => {
            if (pkg.unlimited_stock) return sum + 999999
            return sum + (pkg.manual_quantity || 0)
          }, 0)

          const hasUnlimited = manualInventoryPackages.some(pkg => pkg.unlimited_stock)

          inventoryItems.push(
            <div key="manual" className="flex items-center space-x-1 space-x-reverse">
              <Package className="w-3 h-3 text-green-400" />
              <span className="text-xs">
                {hasUnlimited ? 'غير محدود' : `${totalManualStock} قطعة`}
              </span>
            </div>
          )
        }

        // Show digital codes status
        if (digitalPackages.length > 0) {
          const availableCodes = digitalPackages.reduce((sum, pkg) => sum + (pkg.availableCodesCount || 0), 0)

          inventoryItems.push(
            <div key="digital" className="flex items-center space-x-1 space-x-reverse">
              <Key className="w-3 h-3 text-blue-400" />
              <span className="text-xs">{availableCodes} كود</span>
            </div>
          )
        }

        return (
          <div className="space-y-1">
            {inventoryItems}
          </div>
        )
      }
    },
    {
      key: 'status',
      label: 'الحالة',
      render: (product) => (
        <div className="flex space-x-2 space-x-reverse">
          {product.featured && (
            <span className="px-2 py-1 bg-purple-400/10 text-purple-400 rounded-xl text-xs">مميز</span>
          )}
        </div>
      )
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'category',
      label: 'الفئة',
      type: 'select',
      options: (categories || []).map(cat => ({ value: cat.name, label: cat.name }))
    },
    {
      key: 'featured',
      label: 'مميز',
      type: 'boolean'
    }
  ]

  // Handle form submission
  const handleSave = async (productData: any) => {
    const result = modalMode === 'edit' && editingProduct
      ? await updateItem(editingProduct.id, productData)
      : await createItem(productData)

    if (result.success) {
      closeModal()
    }
  }

  return (
    <div className="space-y-4 md:space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl md:text-2xl font-bold">إدارة المنتجات</h2>
        <button
          onClick={openCreateModal}
          className="btn-primary flex items-center gap-2"
        >
          <Plus className="w-5 h-5" />
          إضافة منتج
        </button>
      </div>

      {/* Filters */}
      <AdminFilters
        filters={filters}
        onFiltersChange={updateFilters}
        filterOptions={filterOptions}
        searchPlaceholder="البحث في المنتجات..."
      />

      {/* Products Table */}
      <AdminTable
        columns={columns}
        data={products}
        loading={loading}
        onEdit={openEditModal}
        onDelete={deleteItem}
        actionLoading={actionLoading}
        emptyMessage="لا توجد منتجات"
      />

      {/* Pagination */}
      <AdminPagination
        pagination={pagination}
        onPageChange={goToPage}
        onNext={nextPage}
        onPrev={prevPage}
      />

      {/* Product Modal */}
      <AdminModal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={modalMode === 'edit' ? 'تعديل المنتج' : 'إضافة منتج جديد'}
        size="xl"
      >
        <ProductForm
          formData={formData}
          setFormData={setFormData}
          customFields={customFields}
          setCustomFields={setCustomFields}
          categories={categories}
          categoriesLoading={categoriesLoading}
          onSave={handleSave}
          onCancel={closeModal}
          isLoading={!!actionLoading}
          mode={modalMode}
        />
      </AdminModal>
    </div>
  )
}
