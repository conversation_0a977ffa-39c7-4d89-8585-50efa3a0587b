"use client"

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '../contexts/AuthContext'
import { toast } from 'sonner'

interface UseProtectedRouteOptions {
  redirectTo?: string
  requiredRole?: 'admin' | 'user' | 'worker'
  showToast?: boolean
}

export function useProtectedRoute(options: UseProtectedRouteOptions = {}) {
  const {
    redirectTo = '/',
    requiredRole,
    showToast = true
  } = options

  const { authState } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // Don't redirect while loading
    if (authState.isLoading) return

    // Check if user is authenticated
    if (!authState.isAuthenticated) {
      if (showToast) {
        toast.error('يجب تسجيل الدخول للوصول إلى هذه الصفحة')
      }
      router.push(redirectTo)
      return
    }

    // Check role requirements
    if (requiredRole && authState.user?.role !== requiredRole) {
      if (showToast) {
        toast.error('ليس لديك صلاحية للوصول إلى هذه الصفحة')
      }
      router.push(redirectTo)
      return
    }
  }, [authState.isLoading, authState.isAuthenticated, authState.user?.role, requiredRole, redirectTo, showToast, router])

  return {
    isLoading: authState.isLoading,
    isAuthenticated: authState.isAuthenticated,
    user: authState.user,
    hasRequiredRole: requiredRole ? authState.user?.role === requiredRole : true
  }
}

// Specific hooks for common use cases
export function useRequireAuth(redirectTo?: string) {
  return useProtectedRoute({ redirectTo })
}

export function useRequireAdmin(redirectTo?: string) {
  return useProtectedRoute({
    redirectTo,
    requiredRole: 'admin'
  })
}



// Hook for guest-only routes (redirect authenticated users)
export function useGuestRoute(redirectTo: string = '/profile') {
  const { authState } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!authState.isLoading && authState.isAuthenticated) {
      router.push(redirectTo)
    }
  }, [authState.isLoading, authState.isAuthenticated, redirectTo, router])

  return {
    isLoading: authState.isLoading,
    isGuest: !authState.isAuthenticated
  }
}
