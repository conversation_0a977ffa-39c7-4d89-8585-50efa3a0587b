import { NextRequest } from 'next/server'

// Rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

export interface RateLimitConfig {
  maxRequests: number
  windowMs: number
  skipSuccessfulRequests?: boolean
}

export function rateLimit(config: RateLimitConfig) {
  return (request: NextRequest): { allowed: boolean; remaining: number; resetTime: number } => {
    const key = getClientKey(request)
    const now = Date.now()
    
    // Clean up expired entries
    cleanupExpiredEntries(now)
    
    const entry = rateLimitStore.get(key)
    
    if (!entry || now > entry.resetTime) {
      // First request or window expired
      const resetTime = now + config.windowMs
      rateLimitStore.set(key, { count: 1, resetTime })
      return { allowed: true, remaining: config.maxRequests - 1, resetTime }
    }
    
    if (entry.count >= config.maxRequests) {
      // Rate limit exceeded
      return { allowed: false, remaining: 0, resetTime: entry.resetTime }
    }
    
    // Increment counter
    entry.count++
    rateLimitStore.set(key, entry)
    
    return { 
      allowed: true, 
      remaining: config.maxRequests - entry.count, 
      resetTime: entry.resetTime 
    }
  }
}

function getClientKey(request: NextRequest): string {
  // Use IP address and user agent for rate limiting key
  const ip = request.ip || 
    request.headers.get('x-forwarded-for')?.split(',')[0] || 
    request.headers.get('x-real-ip') || 
    'unknown'
  
  const userAgent = request.headers.get('user-agent') || 'unknown'
  return `${ip}:${userAgent.slice(0, 50)}` // Limit user agent length
}

function cleanupExpiredEntries(now: number) {
  // Clean up expired entries every 100 requests to prevent memory leaks
  if (Math.random() < 0.01) {
    for (const [key, entry] of rateLimitStore.entries()) {
      if (now > entry.resetTime) {
        rateLimitStore.delete(key)
      }
    }
  }
}

// Authentication rate limiting
export const authRateLimit = rateLimit({
  maxRequests: 5, // 5 attempts
  windowMs: 15 * 60 * 1000, // 15 minutes
})

// General API rate limiting
export const apiRateLimit = rateLimit({
  maxRequests: 100, // 100 requests
  windowMs: 15 * 60 * 1000, // 15 minutes
})

// Input sanitization
export function sanitizeInput(input: string): string {
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<[^>]*>/g, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .replace(/data:/gi, '')
    .trim()
}

// SQL injection prevention
export function escapeSqlInput(input: string): string {
  return input.replace(/'/g, "''").replace(/;/g, '\\;')
}

// XSS prevention
export function escapeHtml(input: string): string {
  const map: { [key: string]: string } = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#x27;',
    '/': '&#x2F;',
  }
  
  return input.replace(/[&<>"'/]/g, (s) => map[s])
}

// CSRF token generation
export function generateCSRFToken(): string {
  const array = new Uint8Array(32)
  if (typeof window !== 'undefined' && window.crypto) {
    window.crypto.getRandomValues(array)
  } else {
    // Fallback for server-side
    for (let i = 0; i < array.length; i++) {
      array[i] = Math.floor(Math.random() * 256)
    }
  }
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
}

// Validate CSRF token
export function validateCSRFToken(token: string, sessionToken: string): boolean {
  return token === sessionToken && token.length === 64
}

// Security headers
export const securityHeaders = {
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'X-XSS-Protection': '1; mode=block',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
}

// Password strength validation
export function validatePasswordStrength(password: string): {
  isValid: boolean
  errors: string[]
  score: number
} {
  const errors: string[] = []
  let score = 0
  
  if (password.length < 8) {
    errors.push('كلمة المرور يجب أن تكون 8 أحرف على الأقل')
  } else {
    score += 1
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على حرف صغير')
  } else {
    score += 1
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على حرف كبير')
  } else {
    score += 1
  }
  
  if (!/\d/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على رقم')
  } else {
    score += 1
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على رمز خاص')
  } else {
    score += 1
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    score
  }
}

// Session security
export function generateSecureSessionId(): string {
  return generateCSRFToken() + Date.now().toString(36)
}

// Audit logging
export function logSecurityEvent(event: {
  type: 'auth_attempt' | 'auth_success' | 'auth_failure' | 'rate_limit' | 'suspicious_activity'
  userId?: string
  tenantId?: string
  ip: string
  userAgent: string
  details?: any
}) {
  // In production, send to security monitoring service
  if (process.env.NODE_ENV === 'development') {
    console.log('🔒 Security Event:', {
      timestamp: new Date().toISOString(),
      ...event
    })
  }
}
