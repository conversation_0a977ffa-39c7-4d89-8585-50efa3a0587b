"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from "react"
import { Tenant, TenantResolver, tenantUtils } from "../lib/tenant"

interface TenantContextType {
  // Current tenant state
  tenant: Tenant | null
  isLoading: boolean
  error: string | null

  // Tenant operations
  setTenant: (tenant: Tenant | null) => void
  refreshTenant: () => Promise<void>
  clearError: () => void

  // Theme utilities
  applyTheme: () => void
  getThemeValue: (key: keyof Tenant['theme_config']) => string | undefined

  // Feature flags
  hasFeature: (feature: keyof Tenant['settings']['features']) => boolean
  getLimit: (limit: keyof Tenant['settings']['limits']) => number

  // Tenant utilities
  isMultiTenantMode: boolean
  getTenantFilter: () => { tenant_id: string } | {}
}

const TenantContext = createContext<TenantContextType | undefined>(undefined)

interface TenantProviderProps {
  children: ReactNode
  initialTenant?: Tenant | null
}

export function TenantProvider({ children, initialTenant }: TenantProviderProps) {
  const [tenant, setTenantState] = useState<Tenant | null>(initialTenant || null)
  const [isLoading, setIsLoading] = useState(!initialTenant)
  const [error, setError] = useState<string | null>(null)

  const isMultiTenantMode = tenantUtils.isMultiTenantMode()

  // Initialize tenant from cookies or environment
  useEffect(() => {
    if (initialTenant) {
      setTenantState(initialTenant)
      setIsLoading(false)
      return
    }

    initializeTenant()
  }, [initialTenant])

  // Apply theme when tenant changes
  useEffect(() => {
    if (tenant?.theme_config) {
      tenantUtils.applyTenantTheme(tenant.theme_config)
    }
  }, [tenant])

  const initializeTenant = async () => {
    try {
      setIsLoading(true)
      setError(null)

      console.log('🏢 Initializing tenant...')

      // Try to get tenant from cookies first (set by middleware)
      const tenantId = getCookie('tenant-id')
      const tenantSlug = getCookie('tenant-slug')

      console.log('🍪 Cookies:', { tenantId, tenantSlug })

      let resolvedTenant: Tenant | null = null

      if (tenantId) {
        console.log('🔍 Resolving by tenant ID:', tenantId)
        resolvedTenant = await TenantResolver.getTenantById(tenantId)
        console.log('📋 Resolved by ID:', resolvedTenant?.name)
      } else if (tenantSlug) {
        console.log('🔍 Resolving by tenant slug:', tenantSlug)
        resolvedTenant = await TenantResolver.getTenantBySlug(tenantSlug)
        console.log('📋 Resolved by slug:', resolvedTenant?.name)
      }

      // Fallback to environment resolution
      if (!resolvedTenant) {
        console.log('🔍 Fallback to environment resolution...')
        resolvedTenant = await TenantResolver.resolveTenantFromEnvironment()
        console.log('📋 Resolved from environment:', resolvedTenant?.name)
      }

      // Final fallback to default tenant
      if (!resolvedTenant) {
        console.log('🔍 Final fallback to default tenant...')
        resolvedTenant = await TenantResolver.getDefaultTenant()
        console.log('📋 Resolved default tenant:', resolvedTenant?.name)
      }

      if (!resolvedTenant) {
        console.error('❌ No tenant could be resolved at any level')

        // Create a fallback tenant object for development and production
        resolvedTenant = {
          id: 'caf1844f-4cc2-4c17-a775-1c837ae01051', // Use the actual main tenant ID
          name: 'بنتاكون الرئيسي',
          slug: 'main',
          theme_config: {
            primaryColor: '#3b82f6',
            secondaryColor: '#1e40af',
            accentColor: '#f59e0b',
            backgroundColor: '#111827',
            textColor: '#ffffff'
          },
          settings: {
            features: {
              digitalCodes: true,
              walletSystem: true,
              customFields: true,
              analytics: true
            },
            limits: {
              maxProducts: 1000,
              maxUsers: 10000,
              maxOrders: 100000
            }
          },
          status: 'active' as const,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      }

      console.log('✅ Tenant resolved successfully:', resolvedTenant.name)
      setTenantState(resolvedTenant)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize tenant'
      setError(errorMessage)
      console.error('❌ Tenant initialization error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const setTenant = (newTenant: Tenant | null) => {
    setTenantState(newTenant)
    setError(null)
  }

  const refreshTenant = async () => {
    if (!tenant) return

    try {
      setIsLoading(true)
      setError(null)

      const refreshedTenant = await TenantResolver.getTenantById(tenant.id)
      if (refreshedTenant) {
        setTenantState(refreshedTenant)
      } else {
        throw new Error('Tenant no longer exists')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh tenant'
      setError(errorMessage)
      console.error('Tenant refresh error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const clearError = () => {
    setError(null)
  }

  const applyTheme = () => {
    if (tenant?.theme_config) {
      tenantUtils.applyTenantTheme(tenant.theme_config)
    }
  }

  const getThemeValue = (key: keyof Tenant['theme_config']) => {
    return tenant?.theme_config?.[key]
  }

  const hasFeature = (feature: keyof Tenant['settings']['features']): boolean => {
    return tenant?.settings?.features?.[feature] ?? false
  }

  const getLimit = (limit: keyof Tenant['settings']['limits']): number => {
    return tenant?.settings?.limits?.[limit] ?? 0
  }

  const getTenantFilter = () => {
    if (!isMultiTenantMode || !tenant) {
      return {}
    }
    return tenantUtils.getTenantFilter(tenant.id)
  }

  const value: TenantContextType = {
    // State
    tenant,
    isLoading,
    error,

    // Operations
    setTenant,
    refreshTenant,
    clearError,

    // Theme utilities
    applyTheme,
    getThemeValue,

    // Feature flags
    hasFeature,
    getLimit,

    // Utilities
    isMultiTenantMode,
    getTenantFilter,
  }

  return (
    <TenantContext.Provider value={value}>
      {children}
    </TenantContext.Provider>
  )
}

export function useTenant() {
  const context = useContext(TenantContext)
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider')
  }
  return context
}

// Utility function to get cookie value
function getCookie(name: string): string | null {
  if (typeof document === 'undefined') return null
  
  const value = `; ${document.cookie}`
  const parts = value.split(`; ${name}=`)
  if (parts.length === 2) {
    return parts.pop()?.split(';').shift() || null
  }
  return null
}

// Hook for tenant-aware data fetching
export function useTenantAwareQuery<T>(
  queryFn: (filter: any) => Promise<T>,
  dependencies: any[] = []
) {
  const { getTenantFilter, tenant } = useTenant()
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!tenant) return

    const fetchData = async () => {
      try {
        setLoading(true)
        setError(null)
        const filter = getTenantFilter()
        const result = await queryFn(filter)
        setData(result)
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Query failed'
        setError(errorMessage)
        console.error('Tenant-aware query error:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [tenant, ...dependencies])

  return { data, loading, error, refetch: () => {
    if (tenant) {
      const filter = getTenantFilter()
      queryFn(filter).then(setData).catch(err => {
        const errorMessage = err instanceof Error ? err.message : 'Query failed'
        setError(errorMessage)
      })
    }
  }}
}
