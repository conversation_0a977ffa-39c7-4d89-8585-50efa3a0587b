"use client"

export default function TenantSuspendedPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900">
      <div className="max-w-md w-full mx-auto text-center">
        <div className="bg-gray-800 rounded-lg p-8 shadow-xl">
          <div className="mb-6">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
              <svg
                className="h-6 w-6 text-yellow-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
          </div>
          
          <h1 className="text-2xl font-bold text-white mb-4">
            المتجر معلق مؤقتاً
          </h1>
          
          <p className="text-gray-300 mb-6">
            عذراً، هذا المتجر معلق مؤقتاً ولا يمكن الوصول إليه حالياً. يرجى التواصل مع إدارة المتجر للحصول على مزيد من المعلومات.
          </p>
          
          <div className="mt-6 text-xs text-gray-400">
            <p>للاستفسارات، يرجى التواصل مع الدعم الفني.</p>
          </div>
        </div>
      </div>
    </div>
  )
}
