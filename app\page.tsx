// Server Component for SSR - Better SEO and Performance
import Link from "next/link"
import { Tag } from "lucide-react"
import { createClient } from '@supabase/supabase-js'
import { TenantResolver } from "./lib/tenant"
import CategorySection from "./components/CategorySection"
import PromoBanner from "./components/PromoBanner"
import PaginatedProductSection from "./components/PaginatedProductSection"
import CategoryCard from "./components/CategoryCard"
import HomepageWrapper from "./components/HomepageWrapper"

// Secure server-side Supabase client
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// Server Component - Fetches data at build/request time
export default async function HomePage() {
  console.log('🔄 SSR: Loading homepage data...')

  // Get tenant slug (in production, extract from domain/subdomain)
  const tenantSlug = 'main'

  try {
    // 1. Resolve tenant securely
    const tenant = await TenantResolver.getTenantBySlug(tenantSlug)
    if (!tenant) {
      return (
        <div className="min-h-screen bg-gray-900 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-white mb-4">Tenant not found</h1>
            <p className="text-gray-400">The requested tenant could not be found.</p>
          </div>
        </div>
      )
    }

    console.log('✅ SSR: Tenant resolved:', tenant.name)

    // 2. Fetch all homepage data in parallel (secure server-side)
    const [
      productsResult,
      categoriesResult,
      homepageSectionsResult
    ] = await Promise.allSettled([
      // Products with relations - filtered by tenant
      supabaseAdmin
        .from('products')
        .select(`
          *,
          categories (
            id, name, slug, description, image, created_at, updated_at, tenant_id
          ),
          packages (
            *,
            digital_codes (*)
          ),
          custom_fields (*),
          dropdowns (
            *,
            dropdown_options (*)
          )
        `)
        .eq('tenant_id', tenant.id)
        .order('created_at', { ascending: false }),

      // Categories - filtered by tenant
      supabaseAdmin
        .from('categories')
        .select('*')
        .eq('tenant_id', tenant.id)
        .order('name')
        .limit(6), // Only first 6 for homepage

      // Homepage sections - filtered by tenant
      supabaseAdmin
        .from('homepage_sections')
        .select('*')
        .eq('tenant_id', tenant.id)
        .eq('is_active', true)
        .order('order_index')
    ])

    // Extract data safely
    const products = productsResult.status === 'fulfilled' ? productsResult.value.data || [] : []
    const categories = categoriesResult.status === 'fulfilled' ? categoriesResult.value.data || [] : []
    const homepageSections = homepageSectionsResult.status === 'fulfilled' ? homepageSectionsResult.value.data || [] : []

    console.log('📊 SSR: Data loaded successfully:', {
      products: products.length,
      categories: categories.length,
      homepageSections: homepageSections.length
    })

    // Map products to include missing properties for UI
    const mappedProducts = products.map(product => ({
      ...product,
      rating: product.rating || 4.5,
      commentCount: product.commentCount || 0,
      coverImage: product.cover_image || '/logo.jpg'
    }))

    // Calculate product count for each category
    const categoriesWithProductCount = categories.map(category => {
      const productCount = mappedProducts.filter(product =>
        product.category_id === category.id || product.category === category.name
      ).length
      return { ...category, productCount }
    })

    // Get featured products for homepage
    const featuredProducts = mappedProducts.filter(product => product.featured).slice(0, 8)
    const latestProducts = mappedProducts.slice(0, 12)

    // Get active homepage sections sorted by order
    const activeSections = homepageSections.filter((section) => section.is_active).sort((a, b) => (a.order_index || 0) - (b.order_index || 0))

    return (
      <HomepageWrapper
        initialProducts={mappedProducts}
        initialCategories={categories}
        initialHomepageSections={homepageSections}
        tenant={tenant}
      >
        <div className="min-h-screen bg-gray-900">
          {/* Promotional Banner */}
          <section className="container mx-auto px-4 pt-4 pb-2">
            <PromoBanner />
          </section>

          {/* Categories Section */}
          {categoriesWithProductCount.length > 0 && (
            <section className="container mx-auto px-4 py-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl md:text-2xl font-bold text-white mb-2">
                    🏷️ تصفح حسب الفئة
                  </h2>
                  <p className="text-gray-400">
                    اكتشف منتجاتنا المنظمة حسب الفئات
                  </p>
                </div>
                <Link
                  href="/categories"
                  className="text-purple-400 hover:text-purple-300 transition-colors text-sm font-medium flex items-center space-x-1 space-x-reverse"
                >
                  <span>عرض الكل</span>
                  <Tag className="w-4 h-4" />
                </Link>
              </div>

              {/* Categories Grid */}
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3 md:gap-4">
                {categoriesWithProductCount.map((category) => (
                  <CategoryCard
                    key={category.id}
                    category={category}
                    productCount={category.productCount}
                  />
                ))}
              </div>
            </section>
          )}

          {/* Featured Products Section */}
          {featuredProducts.length > 0 && (
            <section className="container mx-auto px-4 py-6">
              <CategorySection
                title="🌟 المنتجات المميزة"
                products={featuredProducts}
                userRole="user" // SSR doesn't know user role yet
                showViewAll={false}
              />
            </section>
          )}

          {/* Latest Products Section */}
          <section className="container mx-auto px-4 py-6">
            <PaginatedProductSection
              title="🎮 أحدث المنتجات"
              products={latestProducts}
              userRole="user" // SSR doesn't know user role yet
              itemsPerPage={12}
              showPagination={latestProducts.length > 12}
            />
          </section>

          {/* Dynamic Homepage Sections */}
          {activeSections.map((section) => (
            <section key={section.id} className="container mx-auto px-4 py-6">
              <div className="bg-gray-800 rounded-lg p-6">
                <h2 className="text-2xl font-bold text-white mb-4">{section.title}</h2>
                <div
                  className="text-gray-300"
                  dangerouslySetInnerHTML={{ __html: section.content || '' }}
                />
              </div>
            </section>
          ))}

          {/* All Products Link */}
          {mappedProducts.length > 12 && (
            <section className="container mx-auto px-4 py-8 text-center">
              <Link
                href="/shop"
                className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-4 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/25"
              >
                عرض جميع المنتجات ({mappedProducts.length})
              </Link>
            </section>
          )}
        </div>
      </HomepageWrapper>
    )

  } catch (error) {
    console.error('❌ SSR: Error loading homepage:', error)

    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">خطأ في تحميل الصفحة</h1>
          <p className="text-gray-400">حدث خطأ أثناء تحميل بيانات الصفحة الرئيسية</p>
        </div>
      </div>
    )
  }
}
