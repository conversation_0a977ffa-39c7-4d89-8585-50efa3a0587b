import type { Product, Package } from '../types'

export type UserRole = 'user' | 'distributor' | 'admin'

/**
 * Get the appropriate price for a package based on user role
 */
export function getPackagePrice(pkg: Package, userRole: UserRole = 'user'): number {
  // For distributors, show distributor_price if available, otherwise user_price
  if (userRole === 'distributor' && pkg.distributor_price) {
    return pkg.distributor_price
  }
  
  // For regular users, show discount_price if available, otherwise user_price
  if (userRole === 'user' && pkg.discount_price) {
    return pkg.discount_price
  }
  
  // Fallback to user_price
  return pkg.user_price || 0
}

/**
 * Get the original price to show as crossed out (for discount display)
 */
export function getPackageOriginalPrice(pkg: Package, userRole: UserRole = 'user'): number | null {
  // For distributors, show user_price as crossed out if they get distributor_price
  if (userRole === 'distributor' && pkg.distributor_price) {
    return pkg.user_price
  }
  
  // For regular users, show user_price as crossed out if they get discount_price
  if (userRole === 'user' && pkg.discount_price) {
    return pkg.user_price
  }
  
  // No crossed out price needed
  return null
}

/**
 * Check if a package has a discount for the given user role
 */
export function hasPackageDiscount(pkg: Package, userRole: UserRole = 'user'): boolean {
  if (userRole === 'distributor' && pkg.distributor_price && pkg.distributor_price < pkg.user_price) {
    return true
  }
  
  if (userRole === 'user' && pkg.discount_price && pkg.discount_price < pkg.user_price) {
    return true
  }
  
  return false
}

/**
 * Calculate discount percentage for a package
 */
export function getPackageDiscountPercentage(pkg: Package, userRole: UserRole = 'user'): number {
  const originalPrice = getPackageOriginalPrice(pkg, userRole)
  const currentPrice = getPackagePrice(pkg, userRole)
  
  if (!originalPrice || currentPrice >= originalPrice) {
    return 0
  }
  
  return Math.round(((originalPrice - currentPrice) / originalPrice) * 100)
}

/**
 * Get the appropriate price for a product (when no packages exist) based on user role
 */
export function getProductPrice(product: Product, userRole: UserRole = 'user'): number | null {
  // Only show product-level pricing if no packages exist
  if (product.packages && product.packages.length > 0) {
    return null
  }
  
  // For distributors, show distributor_price if available, otherwise user_price
  if (userRole === 'distributor' && product.distributor_price) {
    return product.distributor_price
  }
  
  // For regular users, show discount_price if available, otherwise user_price
  if (userRole === 'user' && product.discount_price) {
    return product.discount_price
  }
  
  // Fallback to user_price
  return product.user_price || null
}

/**
 * Get the original price to show as crossed out for product-level pricing
 */
export function getProductOriginalPrice(product: Product, userRole: UserRole = 'user'): number | null {
  // Only show product-level pricing if no packages exist
  if (product.packages && product.packages.length > 0) {
    return null
  }
  
  // For distributors, show user_price as crossed out if they get distributor_price
  if (userRole === 'distributor' && product.distributor_price && product.user_price) {
    return product.user_price
  }
  
  // For regular users, show user_price as crossed out if they get discount_price
  if (userRole === 'user' && product.discount_price && product.user_price) {
    return product.user_price
  }
  
  // No crossed out price needed
  return null
}

/**
 * Check if a product has pricing available
 */
export function hasProductPricing(product: Product): boolean {
  // If has packages, pricing comes from packages
  if (product.packages && product.packages.length > 0) {
    return product.packages.some(pkg => pkg.user_price > 0)
  }
  
  // Check product-level pricing
  return !!(product.user_price && product.user_price > 0)
}

/**
 * Get the cheapest package for a product based on user role
 */
export function getCheapestPackage(product: Product, userRole: UserRole = 'user'): Package | null {
  if (!product.packages || product.packages.length === 0) {
    return null
  }
  
  return product.packages.reduce((cheapest, pkg) => {
    const currentPrice = getPackagePrice(pkg, userRole)
    const cheapestPrice = getPackagePrice(cheapest, userRole)
    
    return currentPrice < cheapestPrice ? pkg : cheapest
  }, product.packages[0])
}

/**
 * Format price display with discount information
 */
export function formatPriceDisplay(
  currentPrice: number,
  originalPrice: number | null,
  userRole: UserRole = 'user'
): {
  currentPrice: number
  originalPrice: number | null
  hasDiscount: boolean
  discountPercentage: number
} {
  const hasDiscount = originalPrice !== null && currentPrice < originalPrice
  const discountPercentage = hasDiscount
    ? Math.round(((originalPrice! - currentPrice) / originalPrice!) * 100)
    : 0

  return {
    currentPrice,
    originalPrice: hasDiscount ? originalPrice : null,
    hasDiscount,
    discountPercentage
  }
}

/**
 * Unified pricing function for both products and packages
 * This consolidates all pricing logic in one place
 */
export function getUnifiedPrice(
  item: Product | Package,
  userRole: UserRole = 'user',
  priceType: 'current' | 'original' = 'current'
): number | null {
  // For products with packages, don't use product-level pricing
  if ('packages' in item && item.packages && item.packages.length > 0) {
    return null
  }

  // Return original price if requested
  if (priceType === 'original') {
    if ('packages' in item) {
      return getProductOriginalPrice(item, userRole)
    } else {
      return getPackageOriginalPrice(item, userRole)
    }
  }

  // Return current price based on item type
  if ('packages' in item) {
    return getProductPrice(item, userRole)
  } else {
    return getPackagePrice(item, userRole)
  }
}
