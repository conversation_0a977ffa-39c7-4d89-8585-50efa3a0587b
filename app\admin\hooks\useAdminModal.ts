import { useState, useCallback } from 'react'

export interface ModalState<T> {
  isOpen: boolean
  mode: 'create' | 'edit' | 'view'
  item: T | null
}

export function useAdminModal<T extends { id: string }>() {
  const [modalState, setModalState] = useState<ModalState<T>>({
    isOpen: false,
    mode: 'create',
    item: null
  })

  const openModal = useCallback((mode: 'create' | 'edit' | 'view', item?: T) => {
    setModalState({
      isOpen: true,
      mode,
      item: item || null
    })
  }, [])

  const closeModal = useCallback(() => {
    setModalState({
      isOpen: false,
      mode: 'create',
      item: null
    })
  }, [])

  const openCreateModal = useCallback(() => {
    openModal('create')
  }, [openModal])

  const openEditModal = useCallback((item: T) => {
    openModal('edit', item)
  }, [openModal])

  const openViewModal = useCallback((item: T) => {
    openModal('view', item)
  }, [openModal])

  return {
    modalState,
    openModal,
    closeModal,
    openCreateModal,
    openEditModal,
    openViewModal,
    isOpen: modalState.isOpen,
    mode: modalState.mode,
    item: modalState.item
  }
}
