'use client'

import React, { useState, useEffect } from 'react'
import { Eye, EyeOff, Info, AlertCircle, CheckCircle, XCircle } from 'lucide-react'
import type { CustomField, Dropdown, DropdownOption } from '../types'

interface DynamicCustomFieldsProps {
  productId?: string
  packageId?: string
  onFieldsChange: (data: Record<string, any>) => void
  initialData?: Record<string, any>
  className?: string
}

interface FieldValidationState {
  isValid: boolean
  message?: string
  strength?: 'weak' | 'medium' | 'strong' // For password fields
}

export default function DynamicCustomFields({
  productId,
  packageId,
  onFieldsChange,
  initialData = {},
  className = ''
}: DynamicCustomFieldsProps) {
  const [customFields, setCustomFields] = useState<CustomField[]>([])
  const [dropdownFields, setDropdownFields] = useState<Dropdown[]>([])
  const [fieldValues, setFieldValues] = useState<Record<string, any>>(initialData)
  const [fieldValidation, setFieldValidation] = useState<Record<string, FieldValidationState>>({})
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch custom fields and dropdowns
  useEffect(() => {
    const fetchFields = async () => {
      try {
        setLoading(true)
        setError(null)

        // Determine the query parameters based on what's provided
        const params = new URLSearchParams()
        if (productId) params.append('productId', productId)
        if (packageId) params.append('packageId', packageId)

        // Fetch custom fields
        const customFieldsResponse = await fetch(`/api/custom-fields?${params}`)
        if (!customFieldsResponse.ok) {
          throw new Error('Failed to fetch custom fields')
        }
        const customFieldsData = await customFieldsResponse.json()

        // Fetch dropdown fields
        const dropdownsResponse = await fetch(`/api/dropdowns?${params}`)
        if (!dropdownsResponse.ok) {
          throw new Error('Failed to fetch dropdown fields')
        }
        const dropdownsData = await dropdownsResponse.json()

        // Sort fields by field_order
        const sortedCustomFields = (customFieldsData.fields || []).sort(
          (a: CustomField, b: CustomField) => (a.field_order || 0) - (b.field_order || 0)
        )
        const sortedDropdowns = (dropdownsData.dropdowns || []).sort(
          (a: Dropdown, b: Dropdown) => (a.field_order || 0) - (b.field_order || 0)
        )

        setCustomFields(sortedCustomFields)
        setDropdownFields(sortedDropdowns)

        // Initialize field values with defaults
        const initialValues = { ...initialData }
        
        // Set default values for dropdowns
        sortedDropdowns.forEach((dropdown: Dropdown) => {
          if (!initialValues[dropdown.id]) {
            const defaultOption = dropdown.options?.find(opt => opt.is_default)
            if (defaultOption) {
              initialValues[dropdown.id] = defaultOption.value
            }
          }
        })

        setFieldValues(initialValues)
        onFieldsChange(initialValues)

      } catch (err) {
        console.error('Error fetching fields:', err)
        setError(err instanceof Error ? err.message : 'Failed to load fields')
      } finally {
        setLoading(false)
      }
    }

    if (productId || packageId) {
      fetchFields()
    } else {
      setLoading(false)
    }
  }, [productId, packageId, initialData, onFieldsChange])

  // Validate field value
  const validateField = (field: CustomField, value: string): FieldValidationState => {
    const rules = field.validation_rules || {}
    
    // Required field validation
    if (field.required && (!value || value.trim() === '')) {
      return { isValid: false, message: `${field.label} مطلوب` }
    }

    // Skip validation if field is empty and not required
    if (!value || value.trim() === '') {
      return { isValid: true }
    }

    // Pattern validation
    if (rules.pattern) {
      const regex = new RegExp(rules.pattern)
      if (!regex.test(value)) {
        return { isValid: false, message: `${field.label} غير صحيح` }
      }
    }

    // Length validation
    if (rules.minLength && value.length < rules.minLength) {
      return { isValid: false, message: `${field.label} يجب أن يكون ${rules.minLength} أحرف على الأقل` }
    }
    if (rules.maxLength && value.length > rules.maxLength) {
      return { isValid: false, message: `${field.label} يجب أن يكون ${rules.maxLength} أحرف كحد أقصى` }
    }

    // Number validation
    if (field.field_type === 'number') {
      const numValue = parseFloat(value)
      if (isNaN(numValue)) {
        return { isValid: false, message: `${field.label} يجب أن يكون رقم` }
      }
      if (rules.min !== undefined && numValue < rules.min) {
        return { isValid: false, message: `${field.label} يجب أن يكون ${rules.min} أو أكثر` }
      }
      if (rules.max !== undefined && numValue > rules.max) {
        return { isValid: false, message: `${field.label} يجب أن يكون ${rules.max} أو أقل` }
      }
    }

    // Email validation
    if (field.field_type === 'email') {
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
      if (!emailRegex.test(value)) {
        return { isValid: false, message: 'البريد الإلكتروني غير صحيح' }
      }
    }

    // Password strength validation
    if (field.field_type === 'password' && field.display_options?.showStrength) {
      const strength = calculatePasswordStrength(value)
      return { isValid: true, strength }
    }

    return { isValid: true }
  }

  // Calculate password strength
  const calculatePasswordStrength = (password: string): 'weak' | 'medium' | 'strong' => {
    let score = 0
    if (password.length >= 8) score++
    if (/[a-z]/.test(password)) score++
    if (/[A-Z]/.test(password)) score++
    if (/[0-9]/.test(password)) score++
    if (/[^a-zA-Z0-9]/.test(password)) score++

    if (score <= 2) return 'weak'
    if (score <= 4) return 'medium'
    return 'strong'
  }

  // Handle field value change
  const handleFieldChange = (fieldId: string, value: any, field?: CustomField) => {
    const newValues = { ...fieldValues, [fieldId]: value }
    setFieldValues(newValues)
    onFieldsChange(newValues)

    // Validate field if it's a custom field
    if (field) {
      const validation = validateField(field, value)
      setFieldValidation(prev => ({ ...prev, [fieldId]: validation }))
    }
  }

  // Toggle password visibility
  const togglePasswordVisibility = (fieldId: string) => {
    setShowPasswords(prev => ({ ...prev, [fieldId]: !prev[fieldId] }))
  }

  // Render validation icon
  const renderValidationIcon = (fieldId: string) => {
    const validation = fieldValidation[fieldId]
    if (!validation) return null

    if (validation.isValid) {
      return <CheckCircle className="w-5 h-5 text-green-500" />
    } else {
      return <XCircle className="w-5 h-5 text-red-500" />
    }
  }

  // Render password strength indicator
  const renderPasswordStrength = (strength?: 'weak' | 'medium' | 'strong') => {
    if (!strength) return null

    const colors = {
      weak: 'bg-red-500',
      medium: 'bg-yellow-500',
      strong: 'bg-green-500'
    }

    const labels = {
      weak: 'ضعيف',
      medium: 'متوسط',
      strong: 'قوي'
    }

    return (
      <div className="mt-2">
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <div className="flex space-x-1 rtl:space-x-reverse">
            {[1, 2, 3].map((level) => (
              <div
                key={level}
                className={`w-6 h-2 rounded ${
                  (strength === 'weak' && level === 1) ||
                  (strength === 'medium' && level <= 2) ||
                  (strength === 'strong' && level <= 3)
                    ? colors[strength]
                    : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
          <span className={`text-sm ${colors[strength].replace('bg-', 'text-')}`}>
            {labels[strength]}
          </span>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        {[1, 2, 3].map((i) => (
          <div key={i} className="animate-pulse">
            <div className="h-4 bg-gray-300 rounded w-1/4 mb-2"></div>
            <div className="h-10 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <AlertCircle className="w-5 h-5 text-red-500" />
          <span className="text-red-700">{error}</span>
        </div>
      </div>
    )
  }

  // Combine and sort all fields
  const allFields = [
    ...customFields.map(field => ({ ...field, type: 'custom' as const })),
    ...dropdownFields.map(field => ({ ...field, type: 'dropdown' as const }))
  ].sort((a, b) => (a.field_order || 0) - (b.field_order || 0))

  if (allFields.length === 0) {
    return null // No fields to display
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {allFields.map((field) => {
        const fieldId = field.id
        const validation = fieldValidation[fieldId]
        const isInvalid = validation && !validation.isValid

        if (field.type === 'dropdown') {
          const dropdownField = field as Dropdown
          return (
            <div key={fieldId} className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                {dropdownField.label}
                {dropdownField.required && <span className="text-red-500 mr-1">*</span>}
              </label>
              
              {dropdownField.description && (
                <p className="text-sm text-gray-600 leading-relaxed">
                  {dropdownField.description}
                </p>
              )}

              <div className="relative">
                <select
                  value={fieldValues[fieldId] || ''}
                  onChange={(e) => handleFieldChange(fieldId, e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                    isInvalid ? 'border-red-500' : 'border-gray-300'
                  }`}
                  required={dropdownField.required}
                >
                  <option value="">
                    {dropdownField.display_options?.placeholder || `اختر ${dropdownField.label}`}
                  </option>
                  {dropdownField.options?.map((option) => (
                    <option key={option.id} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                
                {dropdownField.display_options?.helpText && (
                  <div className="mt-1 flex items-center space-x-1 rtl:space-x-reverse">
                    <Info className="w-4 h-4 text-gray-400" />
                    <span className="text-xs text-gray-500">
                      {dropdownField.display_options.helpText}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )
        } else {
          const customField = field as CustomField
          const fieldType = customField.field_type || customField.type || 'text'
          const showPassword = showPasswords[fieldId]

          return (
            <div key={fieldId} className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                {customField.label}
                {customField.required && <span className="text-red-500 mr-1">*</span>}
              </label>
              
              {customField.description && (
                <p className="text-sm text-gray-600 leading-relaxed">
                  {customField.description}
                </p>
              )}

              <div className="relative">
                {fieldType === 'textarea' ? (
                  <textarea
                    value={fieldValues[fieldId] || ''}
                    onChange={(e) => handleFieldChange(fieldId, e.target.value, customField)}
                    placeholder={customField.placeholder}
                    rows={customField.display_options?.rows || 3}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-vertical ${
                      isInvalid ? 'border-red-500' : 'border-gray-300'
                    }`}
                    required={customField.required}
                  />
                ) : (
                  <input
                    type={fieldType === 'password' && showPassword ? 'text' : fieldType}
                    value={fieldValues[fieldId] || ''}
                    onChange={(e) => handleFieldChange(fieldId, e.target.value, customField)}
                    placeholder={customField.placeholder}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                      isInvalid ? 'border-red-500' : 'border-gray-300'
                    } ${fieldType === 'password' ? 'pr-10' : ''}`}
                    required={customField.required}
                    minLength={customField.validation_rules?.minLength}
                    maxLength={customField.validation_rules?.maxLength}
                    min={customField.validation_rules?.min}
                    max={customField.validation_rules?.max}
                    pattern={customField.validation_rules?.pattern}
                  />
                )}

                {/* Password visibility toggle */}
                {fieldType === 'password' && (
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility(fieldId)}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                )}

                {/* Validation icon */}
                {fieldValues[fieldId] && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    {renderValidationIcon(fieldId)}
                  </div>
                )}
              </div>

              {/* Validation message */}
              {isInvalid && validation?.message && (
                <p className="text-sm text-red-600 flex items-center space-x-1 rtl:space-x-reverse">
                  <AlertCircle className="w-4 h-4" />
                  <span>{validation.message}</span>
                </p>
              )}

              {/* Password strength indicator */}
              {fieldType === 'password' && 
               customField.display_options?.showStrength && 
               fieldValues[fieldId] && 
               validation?.strength && (
                renderPasswordStrength(validation.strength)
              )}

              {/* Help text */}
              {customField.display_options?.helpText && (
                <div className="flex items-center space-x-1 rtl:space-x-reverse">
                  <Info className="w-4 h-4 text-gray-400" />
                  <span className="text-xs text-gray-500">
                    {customField.display_options.helpText}
                  </span>
                </div>
              )}
            </div>
          )
        }
      })}
    </div>
  )
}
