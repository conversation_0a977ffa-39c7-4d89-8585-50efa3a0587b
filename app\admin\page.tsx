"use client"

import { useState, useEffect } from "react"
import { Users, Package, BarChart3, DollarSign, ShoppingCart, Home, Menu, X, Tag, Coins, TrendingUp } from "lucide-react"
import {
  LazyProductCRUD,
  LazyUserManagement,
  LazyHomepageManagement,
  LazyOrderManagement
} from "../components/LazyComponents"
import CategoryManagement from "./components/CategoryManagement"
import { convertAndFormatPrice } from "../utils/currency"
import { useData } from "../contexts/DataContext"
import { calculateTotalEarnings, calculateEarningsByPeriod, getTopProfitableProducts, getEarningsByCurrency, formatEarningsData } from "../utils/earnings"
// import { Money } from "../components/Money"
import dynamic from "next/dynamic"

// Lazy load the currencies component
const CurrenciesAdmin = dynamic(() => import('./currencies/page'), {
  loading: () => <div className="p-6">جاري تحميل إدارة العملات...</div>
})

export default function AdminDashboard() {
  // Use centralized data context
  const { products, users, orders, currentUser } = useData()

  // Set default tab based on user role
  const defaultTab = currentUser?.role === "worker" ? "orders" : "overview"
  const [activeTab, setActiveTab] = useState(defaultTab)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  // Check if current user has admin or worker access
  const isAdmin = currentUser?.role === "admin"
  const isWorker = currentUser?.role === "worker"
  const hasAccess = isAdmin || isWorker

  // Define all available tabs
  const allTabs = [
    { id: "overview", label: "نظرة عامة", icon: BarChart3, adminOnly: true },
    { id: "products", label: "المنتجات", icon: Package, adminOnly: true },
    { id: "categories", label: "الفئات", icon: Tag, adminOnly: true },
    { id: "currencies", label: "العملات", icon: Coins, adminOnly: true },
    { id: "users", label: "المستخدمون", icon: Users, adminOnly: true },
    { id: "orders", label: "الطلبات", icon: ShoppingCart, adminOnly: false },
    { id: "homepage", label: "الصفحة الرئيسية", icon: Home, adminOnly: true },
  ]

  // Filter tabs based on user role
  const tabs = isWorker
    ? allTabs.filter(tab => !tab.adminOnly)  // Workers only see non-admin tabs (orders)
    : allTabs  // Admins see all tabs

  // Update active tab when user role changes - ALWAYS call useEffect
  useEffect(() => {
    if (isWorker && activeTab !== "orders") {
      setActiveTab("orders")
    }
  }, [isWorker, activeTab])

  // Early return after all hooks have been called
  if (!hasAccess) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-2xl font-bold mb-4">الوصول مرفوض</h1>
        <p className="text-gray-400">ليس لديك صلاحية للوصول إلى هذه الصفحة.</p>
      </div>
    )
  }

  // Calculate earnings data
  const allPackages = products.flatMap(p => p.packages || [])
  const earningsData = calculateTotalEarnings(orders, products, allPackages)
  const monthlyEarnings = calculateEarningsByPeriod(orders, products, allPackages, 30)
  const topProducts = getTopProfitableProducts(orders, products, allPackages, 3)
  const currencyBreakdown = getEarningsByCurrency(orders, products, allPackages)

  const stats = {
    totalProducts: products.length,
    totalUsers: users.length,
    totalOrders: orders.length,
    totalRevenue: earningsData.totalRevenue,
    totalProfit: earningsData.totalProfit,
    profitMargin: earningsData.profitMargin,
    pendingOrders: orders.filter((o) => o.status === "pending").length,
    completedOrders: orders.filter((o) => o.status === "completed").length,
    monthlyProfit: monthlyEarnings.totalProfit,
    monthlyRevenue: monthlyEarnings.totalRevenue,
  }

  return (
    <div className="container mx-auto px-4 py-4 md:py-8">
      {/* Header */}
      <div className="mb-6 md:mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-2 md:mb-4 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
              لوحة التحكم الإدارية
            </h1>
            <p className="text-gray-400 text-sm md:text-lg">إدارة متجرك والمنتجات والمستخدمين والصفحة الرئيسية</p>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="lg:hidden p-2 rounded-xl bg-gray-800/50 backdrop-blur-md border border-gray-700/50 text-gray-400 hover:text-white transition-colors"
            aria-label="فتح القائمة"
          >
            {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>
      </div>

      <div className="flex flex-col lg:grid lg:grid-cols-4 gap-6 lg:gap-8">
        {/* Mobile Navigation Overlay */}
        {isMobileMenuOpen && (
          <div className="lg:hidden fixed inset-0 z-50 bg-black/50 backdrop-blur-sm" onClick={() => setIsMobileMenuOpen(false)}>
            <div className="fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-gray-900/95 backdrop-blur-md border-l border-gray-700/50 shadow-2xl">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-white">القائمة</h2>
                  <button
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="p-2 rounded-lg text-gray-400 hover:text-white hover:bg-gray-700/50 transition-colors"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
                <nav className="space-y-3">
                  {tabs.map((tab) => {
                    const Icon = tab.icon
                    return (
                      <button
                        key={tab.id}
                        onClick={() => {
                          setActiveTab(tab.id)
                          setIsMobileMenuOpen(false)
                        }}
                        className={`w-full flex items-center space-x-3 space-x-reverse px-4 py-4 rounded-xl transition-all duration-300 ${
                          activeTab === tab.id
                            ? "bg-purple-600 text-white shadow-lg shadow-purple-500/25"
                            : "text-gray-400 hover:text-white hover:bg-gray-700/50"
                        }`}
                      >
                        <Icon className="w-6 h-6" />
                        <span className="text-lg">{tab.label}</span>
                      </button>
                    )
                  })}
                </nav>
              </div>
            </div>
          </div>
        )}

        {/* Desktop Sidebar Navigation */}
        <div className="hidden lg:block lg:col-span-1">
          <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 p-4 shadow-xl sticky top-6">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 space-x-reverse px-4 py-3 rounded-xl transition-all duration-300 ${
                      activeTab === tab.id
                        ? "bg-purple-600 text-white shadow-lg shadow-purple-500/25"
                        : "text-gray-400 hover:text-white hover:bg-gray-700/50"
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{tab.label}</span>
                  </button>
                )
              })}
            </nav>
          </div>
        </div>

        {/* Mobile Tab Bar */}
        <div className="lg:hidden order-last">
          <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 p-2 shadow-xl">
            <div className="grid grid-cols-4 gap-1">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex flex-col items-center space-y-1 px-2 py-3 rounded-lg transition-all duration-300 ${
                      activeTab === tab.id
                        ? "bg-purple-600 text-white shadow-lg shadow-purple-500/25"
                        : "text-gray-400 hover:text-white hover:bg-gray-700/50"
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="text-xs font-medium">{tab.label}</span>
                  </button>
                )
              })}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3 min-h-0">
          {activeTab === "overview" && (
            <div className="space-y-6">
              {/* Stats Cards */}
              <div className="grid grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-3 md:gap-6">
                <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-gray-700/50 shadow-xl hover:shadow-purple-500/10 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-xs md:text-sm">إجمالي المنتجات</p>
                      <p className="text-xl md:text-2xl font-bold">{stats.totalProducts}</p>
                    </div>
                    <Package className="w-6 h-6 md:w-8 md:h-8 text-purple-400" />
                  </div>
                </div>

                <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-gray-700/50 shadow-xl hover:shadow-blue-500/10 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-xs md:text-sm">إجمالي المستخدمين</p>
                      <p className="text-xl md:text-2xl font-bold">{stats.totalUsers}</p>
                    </div>
                    <Users className="w-6 h-6 md:w-8 md:h-8 text-blue-400" />
                  </div>
                </div>

                <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-gray-700/50 shadow-xl hover:shadow-green-500/10 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-xs md:text-sm">إجمالي الطلبات</p>
                      <p className="text-xl md:text-2xl font-bold">{stats.totalOrders}</p>
                    </div>
                    <ShoppingCart className="w-6 h-6 md:w-8 md:h-8 text-green-400" />
                  </div>
                </div>

                <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-gray-700/50 shadow-xl hover:shadow-yellow-500/10 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-xs md:text-sm">إجمالي الإيرادات</p>
                      <p className="text-xl md:text-2xl font-bold">{convertAndFormatPrice(stats.totalRevenue)}</p>
                    </div>
                    <DollarSign className="w-6 h-6 md:w-8 md:h-8 text-yellow-400" />
                  </div>
                </div>
              </div>

              {/* Profit Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-6">
                <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-gray-700/50 shadow-xl hover:shadow-emerald-500/10 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-xs md:text-sm">إجمالي الأرباح</p>
                      <p className="text-xl md:text-2xl font-bold text-emerald-400">{convertAndFormatPrice(stats.totalProfit)}</p>
                      <p className="text-xs text-gray-500 mt-1">هامش ربح: {stats.profitMargin.toFixed(1)}%</p>
                    </div>
                    <TrendingUp className="w-6 h-6 md:w-8 md:h-8 text-emerald-400" />
                  </div>
                </div>

                <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-gray-700/50 shadow-xl hover:shadow-blue-500/10 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-xs md:text-sm">أرباح الشهر</p>
                      <p className="text-xl md:text-2xl font-bold text-blue-400">{convertAndFormatPrice(stats.monthlyProfit)}</p>
                      <p className="text-xs text-gray-500 mt-1">من إيرادات: {convertAndFormatPrice(stats.monthlyRevenue)}</p>
                    </div>
                    <BarChart3 className="w-6 h-6 md:w-8 md:h-8 text-blue-400" />
                  </div>
                </div>
              </div>

              {/* Recent Orders */}
              <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 shadow-xl">
                <div className="p-4 md:p-6 border-b border-gray-700/50">
                  <h2 className="text-lg md:text-xl font-bold">الطلبات الأخيرة</h2>
                </div>
                <div className="p-4 md:p-6">
                  {/* Desktop Table */}
                  <div className="hidden md:block overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="text-right text-gray-400 text-sm">
                          <th className="pb-3">رقم الطلب</th>
                          <th className="pb-3">المنتج</th>
                          <th className="pb-3">المبلغ</th>
                          <th className="pb-3">الحالة</th>
                          <th className="pb-3">التاريخ</th>
                        </tr>
                      </thead>
                      <tbody className="space-y-2">
                        {orders.slice(0, 5).map((order) => {
                          const product = products.find((p) => p.id === order.productId)
                          return (
                            <tr key={order.id} className="border-t border-gray-700/50">
                              <td className="py-3 font-mono text-sm">#{order.id}</td>
                              <td className="py-3">{product?.title || "غير معروف"}</td>
                              <td className="py-3 font-semibold">${order.amount}</td>
                              <td className="py-3">
                                <span
                                  className={`px-2 py-1 rounded-lg text-xs ${
                                    order.status === "completed"
                                      ? "bg-green-400/10 text-green-400"
                                      : order.status === "pending"
                                        ? "bg-yellow-400/10 text-yellow-400"
                                        : "bg-red-400/10 text-red-400"
                                  }`}
                                >
                                  {order.status === "completed"
                                    ? "مكتمل"
                                    : order.status === "pending"
                                      ? "قيد الانتظار"
                                      : "فاشل"}
                                </span>
                              </td>
                              <td className="py-3 text-sm text-gray-400">
                                {new Date(order.createdAt).toLocaleDateString("ar")}
                              </td>
                            </tr>
                          )
                        })}
                      </tbody>
                    </table>
                  </div>

                  {/* Mobile Cards */}
                  <div className="md:hidden space-y-3">
                    {orders.slice(0, 5).map((order) => {
                      const product = products.find((p) => p.id === order.productId)
                      return (
                        <div key={order.id} className="bg-gray-700/30 backdrop-blur-sm rounded-xl p-4 border border-gray-600/50">
                          <div className="flex items-center justify-between mb-3">
                            <span className="font-mono text-sm text-gray-400">#{order.id}</span>
                            <span
                              className={`px-2 py-1 rounded-lg text-xs ${
                                order.status === "completed"
                                  ? "bg-green-400/10 text-green-400"
                                  : order.status === "pending"
                                    ? "bg-yellow-400/10 text-yellow-400"
                                    : "bg-red-400/10 text-red-400"
                              }`}
                            >
                              {order.status === "completed"
                                ? "مكتمل"
                                : order.status === "pending"
                                  ? "قيد الانتظار"
                                  : "فاشل"}
                            </span>
                          </div>
                          <div className="space-y-2">
                            <div>
                              <p className="text-sm text-gray-400">المنتج</p>
                              <p className="font-medium">{product?.title || "غير معروف"}</p>
                            </div>
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="text-sm text-gray-400">المبلغ</p>
                                <p className="font-semibold text-lg">${order.amount}</p>
                              </div>
                              <div className="text-left">
                                <p className="text-sm text-gray-400">التاريخ</p>
                                <p className="text-sm">{new Date(order.createdAt).toLocaleDateString("ar")}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </div>

              {/* Currency Breakdown */}
              {currencyBreakdown.length > 0 && (
                <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 shadow-xl">
                  <div className="p-4 md:p-6 border-b border-gray-700/50">
                    <h2 className="text-lg md:text-xl font-bold">الأرباح حسب العملة</h2>
                  </div>
                  <div className="p-4 md:p-6">
                    <div className="space-y-4">
                      {currencyBreakdown.map((currencyData) => (
                        <div key={currencyData.currency} className="flex items-center justify-between p-4 bg-gray-700/30 rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                              {currencyData.currency}
                            </div>
                            <div>
                              <p className="font-semibold">{currencyData.orderCount} طلب</p>
                              <p className="text-sm text-gray-400">
                                {currencyData.originalCurrencyAmount.toFixed(2)} {currencyData.currency}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-emerald-400">${currencyData.totalProfitUSD.toFixed(2)}</p>
                            <p className="text-sm text-gray-400">هامش: {currencyData.profitMarginPercent.toFixed(1)}%</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Top Profitable Products */}
              {topProducts.length > 0 && (
                <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 shadow-xl">
                  <div className="p-4 md:p-6 border-b border-gray-700/50">
                    <h2 className="text-lg md:text-xl font-bold">أكثر المنتجات ربحية</h2>
                  </div>
                  <div className="p-4 md:p-6">
                    <div className="space-y-4">
                      {topProducts.map((productData, index) => (
                        <div key={productData.productId} className="flex items-center justify-between p-4 bg-gray-700/30 rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                              {index + 1}
                            </div>
                            <div>
                              <p className="font-semibold">{productData.productName}</p>
                              <p className="text-sm text-gray-400">{productData.orderCount} طلب</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-emerald-400">{convertAndFormatPrice(productData.totalProfit)}</p>
                            <p className="text-sm text-gray-400">هامش: {productData.profitMargin.toFixed(1)}%</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === "products" && <LazyProductCRUD />}

          {activeTab === "categories" && <CategoryManagement />}

          {activeTab === "currencies" && <CurrenciesAdmin />}

          {activeTab === "users" && <LazyUserManagement />}

          {activeTab === "orders" && <LazyOrderManagement />}

          {activeTab === "homepage" && <LazyHomepageManagement />}
        </div>
      </div>
    </div>
  )
}
