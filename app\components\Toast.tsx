"use client"

import { toast, Toaster } from "sonner"
import { CheckCircle, XCircle, AlertCircle, Info } from "lucide-react"

// Toast notification functions
export const showToast = {
  success: (message: string, description?: string) => {
    toast.success(message, {
      description,
      icon: <CheckCircle className="w-4 h-4" />,
      duration: 4000,
    })
  },

  error: (message: string, description?: string) => {
    toast.error(message, {
      description,
      icon: <XCircle className="w-4 h-4" />,
      duration: 6000,
    })
  },

  warning: (message: string, description?: string) => {
    toast.warning(message, {
      description,
      icon: <AlertCircle className="w-4 h-4" />,
      duration: 5000,
    })
  },

  info: (message: string, description?: string) => {
    toast.info(message, {
      description,
      icon: <Info className="w-4 h-4" />,
      duration: 4000,
    })
  },

  loading: (message: string) => {
    return toast.loading(message)
  },

  promise: <T,>(
    promise: Promise<T>,
    {
      loading,
      success,
      error,
    }: {
      loading: string
      success: string | ((data: T) => string)
      error: string | ((error: any) => string)
    }
  ) => {
    return toast.promise(promise, {
      loading,
      success,
      error,
    })
  },
}

// Custom Toaster component with Arabic support
export function ToastProvider() {
  return (
    <Toaster
      position="top-center"
      dir="rtl"
      theme="dark"
      richColors
      closeButton
      toastOptions={{
        style: {
          background: 'rgb(31 41 55)', // gray-800
          border: '1px solid rgb(55 65 81)', // gray-700
          color: 'rgb(243 244 246)', // gray-100
          fontFamily: 'Cairo, sans-serif',
        },
        className: 'toast-rtl',
      }}
    />
  )
}

// Hook for easier toast usage
export function useToast() {
  return showToast
}
