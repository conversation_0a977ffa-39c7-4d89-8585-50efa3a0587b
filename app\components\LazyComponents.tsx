"use client"

import dynamic from 'next/dynamic'
import { ComponentType } from 'react'
import { FormSkeleton, TableSkeleton, StatsSkeleton } from './LoadingStates'

// Lazy load admin components with loading states
export const LazyProductCRUD = dynamic(
  () => import('../admin/components/ProductCRUD'),
  {
    loading: () => (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="h-8 bg-gray-700 rounded w-48 animate-pulse"></div>
          <div className="h-10 bg-gray-700 rounded w-32 animate-pulse"></div>
        </div>
        <TableSkeleton rows={6} columns={4} />
      </div>
    ),
    ssr: false,
  }
)

export const LazyUserManagement = dynamic(
  () => import('../admin/components/UserManagement'),
  {
    loading: () => (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="h-8 bg-gray-700 rounded w-48 animate-pulse"></div>
          <div className="h-10 bg-gray-700 rounded w-32 animate-pulse"></div>
        </div>
        <TableSkeleton rows={5} columns={5} />
      </div>
    ),
    ssr: false,
  }
)

export const LazyOrderManagement = dynamic(
  () => import('../admin/components/OrderManagement'),
  {
    loading: () => (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="h-8 bg-gray-700 rounded w-48 animate-pulse"></div>
          <div className="flex gap-4">
            <div className="h-10 bg-gray-700 rounded w-32 animate-pulse"></div>
            <div className="h-10 bg-gray-700 rounded w-24 animate-pulse"></div>
          </div>
        </div>
        <TableSkeleton rows={8} columns={6} />
      </div>
    ),
    ssr: false,
  }
)

export const LazyHomepageManagement = dynamic(
  () => import('../admin/components/HomepageManagement'),
  {
    loading: () => (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="h-8 bg-gray-700 rounded w-48 animate-pulse"></div>
          <div className="flex gap-2">
            <div className="h-8 bg-gray-700 rounded w-20 animate-pulse"></div>
            <div className="h-8 bg-gray-700 rounded w-20 animate-pulse"></div>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="bg-gray-800 rounded-lg p-4 animate-pulse">
              <div className="aspect-video bg-gray-700 rounded mb-4"></div>
              <div className="h-4 bg-gray-700 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-700 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    ),
    ssr: false,
  }
)

// Lazy load other heavy components

export const LazyProductCard = dynamic(
  () => import('./ProductCard'),
  {
    loading: () => (
      <div className="bg-gray-800 rounded-lg overflow-hidden border border-gray-700/30 animate-pulse">
        <div className="aspect-square bg-gray-700"></div>
        <div className="p-4 space-y-3">
          <div className="h-4 bg-gray-700 rounded w-3/4"></div>
          <div className="h-3 bg-gray-700 rounded w-1/2"></div>
          <div className="flex justify-between items-center">
            <div className="h-4 bg-gray-700 rounded w-1/4"></div>
            <div className="h-6 bg-gray-700 rounded w-16"></div>
          </div>
        </div>
      </div>
    ),
    ssr: false,
  }
)

// Higher-order component for lazy loading with error boundary
export function withLazyLoading<P extends object>(
  Component: ComponentType<P>,
  LoadingComponent: ComponentType = () => <div>Loading...</div>
) {
  return dynamic(() => Promise.resolve(Component), {
    loading: () => <LoadingComponent />,
    ssr: false,
  })
}

// Preload functions for better UX
export const preloadAdminComponents = () => {
  // Preload admin components when user hovers over admin link
  ;(LazyProductCRUD as any).preload?.()
  ;(LazyUserManagement as any).preload?.()
  ;(LazyOrderManagement as any).preload?.()
  ;(LazyHomepageManagement as any).preload?.()
}

export const preloadShopComponents = () => {
  // Preload shop components when user navigates to shop
  ;(LazyProductCard as any).preload?.()
}

export const preloadHomeComponents = () => {
  // Preload home components
  ;(LazyProductCard as any).preload?.()
}
