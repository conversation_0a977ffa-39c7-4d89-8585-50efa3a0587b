"use client"

import Link from "next/link"
import Image from "next/image"
import type { Category } from "../types"

interface CategoryCardProps {
  category: Category
  productCount?: number
}

export default function CategoryCard({ category, productCount = 0 }: CategoryCardProps) {
  return (
    <Link href={`/category/${category.slug}`}>
      <div className="bg-gray-800 rounded-lg overflow-hidden hover:shadow-xl transition-all duration-300 hover:scale-[1.02] border border-gray-700/30 h-full flex flex-col group">
        {/* Category Image - Square aspect ratio (1:1) */}
        <div className="relative aspect-square overflow-hidden">
          <Image
            src={category.image}
            alt={category.name || category.slug}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            sizes="(max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw"
            onError={(e) => {
              const target = e.target as HTMLImageElement
              target.src = "/logo.jpg"
            }}
          />

          {/* Bottom gradient overlay */}
          <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />
          
          {/* Product count badge */}
          {productCount > 0 && (
            <div className="absolute top-2 right-2 z-10">
              <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-2 py-1 rounded text-xs font-bold shadow-lg">
                {productCount} منتج
              </div>
            </div>
          )}
        </div>

        {/* Category Info - 2:1 aspect ratio (twice as wide as tall) */}
        <div className="aspect-[2/1] p-3 flex flex-col justify-center bg-gray-800">
          {/* Category Name */}
          <h3 className="text-sm font-semibold text-white mb-1 line-clamp-2 leading-tight text-center">
            {category.name || category.slug}
          </h3>

          {/* Category Description */}
          {category.description && (
            <p className="text-xs text-gray-400 line-clamp-2 text-center">
              {category.description}
            </p>
          )}
        </div>
      </div>
    </Link>
  )
}
