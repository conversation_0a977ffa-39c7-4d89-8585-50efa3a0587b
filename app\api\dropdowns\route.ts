import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Get query parameters
    const url = new URL(request.url)
    const productId = url.searchParams.get('productId')
    const packageId = url.searchParams.get('packageId')
    
    // First try to get tenant from headers (set by middleware)
    let tenantId = request.headers.get('x-tenant-id')
    
    // If no tenant in headers, try to get from user profile
    if (!tenantId) {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        
        if (user) {
          const { data: profile } = await supabase
            .from('user_profiles')
            .select('tenant_id')
            .eq('id', user.id)
            .single()
          
          tenantId = profile?.tenant_id
        }
      } catch (authError) {
        // No authenticated user, will use main tenant fallback
      }
    }
    
    // If still no tenant, get main tenant as fallback
    if (!tenantId) {
      const { data: mainTenant } = await supabase
        .from('tenants')
        .select('id')
        .eq('slug', 'main')
        .single()
      
      tenantId = mainTenant?.id || 'caf1844f-4cc2-4c17-a775-1c837ae01051'
    }

    // Build query for dropdowns with options
    let dropdownsQuery = supabase
      .from('dropdowns')
      .select(`
        id,
        label,
        description,
        required,
        field_order,
        validation_rules,
        display_options,
        dropdown_options (
          id,
          label,
          description,
          display_order,
          is_default
        )
      `)
      .eq('tenant_id', tenantId)
      .order('field_order', { ascending: true })

    // Filter by product or package
    if (productId) {
      dropdownsQuery = dropdownsQuery.eq('product_id', productId)
    } else if (packageId) {
      dropdownsQuery = dropdownsQuery.eq('package_id', packageId)
    }

    const { data: dropdowns, error } = await dropdownsQuery

    if (error) {
      console.error('Error fetching dropdowns:', error)
      return NextResponse.json({ error: 'Failed to fetch dropdowns' }, { status: 500 })
    }

    // Sort dropdown options by display_order
    const processedDropdowns = (dropdowns || []).map(dropdown => ({
      ...dropdown,
      options: (dropdown.dropdown_options || []).sort((a: any, b: any) => a.display_order - b.display_order)
    }))

    return NextResponse.json({
      dropdowns: processedDropdowns,
      success: true
    })

  } catch (error) {
    console.error('Dropdowns API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
