"use client"

import Link from "next/link"

export default function TenantNotFoundPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900">
      <div className="max-w-md w-full mx-auto text-center">
        <div className="bg-gray-800 rounded-lg p-8 shadow-xl">
          <div className="mb-6">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
              <svg
                className="h-6 w-6 text-red-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
          </div>
          
          <h1 className="text-2xl font-bold text-white mb-4">
            المتجر غير موجود
          </h1>
          
          <p className="text-gray-300 mb-6">
            عذراً، لم نتمكن من العثور على المتجر المطلوب. قد يكون الرابط غير صحيح أو أن المتجر غير متاح حالياً.
          </p>
          
          <div className="space-y-4">
            <Link
              href="/"
              className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              العودة للصفحة الرئيسية
            </Link>
            
            <button
              onClick={() => window.location.reload()}
              className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-transparent hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
            >
              إعادة المحاولة
            </button>
          </div>
          
          <div className="mt-6 text-xs text-gray-400">
            <p>إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع الدعم الفني.</p>
          </div>
        </div>
      </div>
    </div>
  )
}
