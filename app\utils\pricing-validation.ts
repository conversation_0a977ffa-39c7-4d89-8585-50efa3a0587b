import type { Product, Package } from '../types'
import { getProductPrice, getPackagePrice, type UserRole } from './pricing'

/**
 * Validation result interface
 */
export interface PricingValidationResult {
  valid: boolean
  errors: string[]
  warnings: string[]
}

/**
 * Validate product pricing structure
 */
export function validateProductPricing(product: Product): PricingValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // Check if product has either packages or product-level pricing
  const hasPackages = product.packages && product.packages.length > 0
  const hasProductPricing = !!(product.user_price && product.user_price > 0)

  if (!hasPackages && !hasProductPricing) {
    errors.push('Product must have either packages or product-level pricing')
  }

  if (hasPackages && hasProductPricing) {
    warnings.push('Product has both packages and product-level pricing. Product-level pricing will be ignored.')
  }

  // Validate product-level pricing if no packages
  if (!hasPackages && hasProductPricing) {
    const productValidation = validateProductLevelPricing(product)
    errors.push(...productValidation.errors)
    warnings.push(...productValidation.warnings)
  }

  // Validate packages if they exist
  if (hasPackages) {
    for (const pkg of product.packages) {
      const packageValidation = validatePackagePricing(pkg)
      errors.push(...packageValidation.errors.map(err => `Package "${pkg.name}": ${err}`))
      warnings.push(...packageValidation.warnings.map(warn => `Package "${pkg.name}": ${warn}`))
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Validate product-level pricing
 */
export function validateProductLevelPricing(product: Product): PricingValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // Check required fields
  if (!product.original_price || product.original_price <= 0) {
    errors.push('Original price is required and must be greater than 0')
  }

  if (!product.user_price || product.user_price <= 0) {
    errors.push('User price is required and must be greater than 0')
  }

  // Validate pricing hierarchy
  if (product.original_price && product.user_price) {
    if (product.user_price <= product.original_price) {
      errors.push('User price must be greater than original price')
    }
  }

  if (product.discount_price) {
    if (product.discount_price <= 0) {
      errors.push('Discount price must be greater than 0 if specified')
    }

    if (product.user_price && product.discount_price >= product.user_price) {
      errors.push('Discount price must be less than user price')
    }

    if (product.original_price && product.discount_price <= product.original_price) {
      errors.push('Discount price must be greater than original price')
    }
  }

  if (product.distributor_price) {
    if (product.distributor_price <= 0) {
      errors.push('Distributor price must be greater than 0 if specified')
    }

    if (product.user_price && product.distributor_price >= product.user_price) {
      errors.push('Distributor price must be less than user price')
    }

    if (product.original_price && product.distributor_price <= product.original_price) {
      errors.push('Distributor price must be greater than original price')
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Validate package pricing
 */
export function validatePackagePricing(pkg: Package): PricingValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // Check required fields
  if (!pkg.original_price || pkg.original_price <= 0) {
    errors.push('Original price is required and must be greater than 0')
  }

  if (!pkg.user_price || pkg.user_price <= 0) {
    errors.push('User price is required and must be greater than 0')
  }

  // Validate pricing hierarchy
  if (pkg.original_price && pkg.user_price) {
    if (pkg.user_price <= pkg.original_price) {
      errors.push('User price must be greater than original price')
    }
  }

  if (pkg.discount_price) {
    if (pkg.discount_price <= 0) {
      errors.push('Discount price must be greater than 0 if specified')
    }

    if (pkg.user_price && pkg.discount_price >= pkg.user_price) {
      errors.push('Discount price must be less than user price')
    }

    if (pkg.original_price && pkg.discount_price <= pkg.original_price) {
      errors.push('Discount price must be greater than original price')
    }
  }

  if (pkg.distributor_price) {
    if (pkg.distributor_price <= 0) {
      errors.push('Distributor price must be greater than 0 if specified')
    }

    if (pkg.user_price && pkg.distributor_price >= pkg.user_price) {
      errors.push('Distributor price must be less than user price')
    }

    if (pkg.original_price && pkg.distributor_price <= pkg.original_price) {
      errors.push('Distributor price must be greater than original price')
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Validate pricing consistency between frontend and backend
 */
export function validatePricingConsistency(
  product: Product,
  userRole: UserRole,
  expectedPrice: number
): PricingValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // Calculate actual price using utility functions
  const actualPrice = getProductPrice(product, userRole)

  if (actualPrice === null) {
    errors.push('Product pricing not available for the specified user role')
  } else if (Math.abs(actualPrice - expectedPrice) > 0.01) {
    errors.push(`Price mismatch: expected ${expectedPrice}, got ${actualPrice}`)
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Validate package pricing consistency
 */
export function validatePackagePricingConsistency(
  pkg: Package,
  userRole: UserRole,
  expectedPrice: number
): PricingValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // Calculate actual price using utility functions
  const actualPrice = getPackagePrice(pkg, userRole)

  if (Math.abs(actualPrice - expectedPrice) > 0.01) {
    errors.push(`Package price mismatch: expected ${expectedPrice}, got ${actualPrice}`)
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Comprehensive pricing validation for order creation
 */
export function validateOrderPricing(
  product: Product,
  packageId: string | null,
  userRole: UserRole,
  quantity: number,
  expectedTotal: number
): PricingValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  if (packageId) {
    // Package-based order
    const pkg = product.packages?.find(p => p.id === packageId)
    if (!pkg) {
      errors.push('Package not found')
      return { valid: false, errors, warnings }
    }

    const packageValidation = validatePackagePricing(pkg)
    errors.push(...packageValidation.errors)
    warnings.push(...packageValidation.warnings)

    if (packageValidation.valid) {
      const unitPrice = getPackagePrice(pkg, userRole)
      const calculatedTotal = unitPrice * quantity
      
      if (Math.abs(calculatedTotal - expectedTotal) > 0.01) {
        errors.push(`Order total mismatch: expected ${expectedTotal}, calculated ${calculatedTotal}`)
      }
    }
  } else {
    // Product-level order
    const productValidation = validateProductLevelPricing(product)
    errors.push(...productValidation.errors)
    warnings.push(...productValidation.warnings)

    if (productValidation.valid) {
      const unitPrice = getProductPrice(product, userRole)
      if (unitPrice === null) {
        errors.push('Product pricing not available')
      } else {
        const calculatedTotal = unitPrice * quantity
        
        if (Math.abs(calculatedTotal - expectedTotal) > 0.01) {
          errors.push(`Order total mismatch: expected ${expectedTotal}, calculated ${calculatedTotal}`)
        }
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}
