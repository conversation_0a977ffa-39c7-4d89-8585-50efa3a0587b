import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Enhanced security configuration
const RATE_LIMIT_MAX_REQUESTS = 10
const RATE_LIMIT_WINDOW_MS = 60000 // 1 minute
const MAX_CODES_PER_REQUEST = 50

// In-memory rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

// Audit logging interface
interface SecurityAuditLog {
  event_type: 'digital_code_access' | 'rate_limit_exceeded' | 'unauthorized_access' | 'suspicious_activity'
  user_id?: string
  tenant_id?: string
  order_id?: string
  ip_address: string
  user_agent: string
  success: boolean
  error_message?: string
  metadata?: Record<string, any>
  timestamp: string
}

// Enhanced rate limiting with IP and user-based tracking
function checkRateLimit(identifier: string): { allowed: boolean; remaining: number } {
  const now = Date.now()
  const entry = rateLimitStore.get(identifier)
  
  // Clean up expired entries
  if (entry && now > entry.resetTime) {
    rateLimitStore.delete(identifier)
  }
  
  const currentEntry = rateLimitStore.get(identifier)
  
  if (!currentEntry) {
    // First request in window
    rateLimitStore.set(identifier, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW_MS
    })
    return { allowed: true, remaining: RATE_LIMIT_MAX_REQUESTS - 1 }
  }
  
  if (currentEntry.count >= RATE_LIMIT_MAX_REQUESTS) {
    return { allowed: false, remaining: 0 }
  }
  
  // Increment count
  currentEntry.count++
  rateLimitStore.set(identifier, currentEntry)
  
  return { allowed: true, remaining: RATE_LIMIT_MAX_REQUESTS - currentEntry.count }
}

// Audit logging function
async function logSecurityEvent(supabase: any, event: SecurityAuditLog) {
  try {
    await supabase
      .from('security_audit_logs')
      .insert(event)
  } catch (error) {
    console.error('Failed to log security event:', error)
  }
}

// Enhanced input validation
function validateOrderId(orderId: string): boolean {
  // UUID v4 validation
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(orderId)
}

// Encryption/Decryption utilities (placeholder - implement with your encryption method)
async function decryptDigitalCode(encryptedCode: string): Promise<string> {
  // TODO: Implement proper decryption using your encryption method
  // For now, return the encrypted code (assuming it's already decrypted in DB)
  return encryptedCode
}

serve(async (req) => {
  // CORS headers
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
  }

  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // Only allow GET requests
  if (req.method !== 'GET') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  try {
    // Extract request metadata
    const url = new URL(req.url)
    const orderId = url.pathname.split('/').pop()
    const clientIP = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown'
    const userAgent = req.headers.get('user-agent') || 'unknown'
    
    // Input validation
    if (!orderId || !validateOrderId(orderId)) {
      return new Response(
        JSON.stringify({ error: 'Invalid order ID format' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Rate limiting (IP + User based)
    const rateLimitKey = `${clientIP}:digital-codes`
    const rateLimit = checkRateLimit(rateLimitKey)
    
    if (!rateLimit.allowed) {
      return new Response(
        JSON.stringify({ 
          error: 'Rate limit exceeded',
          retryAfter: Math.ceil(RATE_LIMIT_WINDOW_MS / 1000)
        }),
        { 
          status: 429, 
          headers: { 
            ...corsHeaders, 
            'Content-Type': 'application/json',
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': String(Math.ceil(Date.now() / 1000) + Math.ceil(RATE_LIMIT_WINDOW_MS / 1000))
          } 
        }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Get JWT token from Authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      await logSecurityEvent(supabase, {
        event_type: 'unauthorized_access',
        order_id: orderId,
        ip_address: clientIP,
        user_agent: userAgent,
        success: false,
        error_message: 'Missing or invalid authorization header',
        timestamp: new Date().toISOString()
      })
      
      return new Response(
        JSON.stringify({ error: 'Unauthorized - Missing token' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    
    // Verify JWT and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      await logSecurityEvent(supabase, {
        event_type: 'unauthorized_access',
        order_id: orderId,
        ip_address: clientIP,
        user_agent: userAgent,
        success: false,
        error_message: 'Invalid or expired token',
        timestamp: new Date().toISOString()
      })
      
      return new Response(
        JSON.stringify({ error: 'Unauthorized - Invalid token' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get user profile and tenant information
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('tenant_id, name, email, role')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      await logSecurityEvent(supabase, {
        event_type: 'unauthorized_access',
        user_id: user.id,
        order_id: orderId,
        ip_address: clientIP,
        user_agent: userAgent,
        success: false,
        error_message: 'User profile not found',
        timestamp: new Date().toISOString()
      })
      
      return new Response(
        JSON.stringify({ error: 'User profile not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Verify order ownership and status
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select('id, user_id, status, tenant_id, product_id, package_id, amount')
      .eq('id', orderId)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (orderError || !order) {
      await logSecurityEvent(supabase, {
        event_type: 'unauthorized_access',
        user_id: user.id,
        tenant_id: profile.tenant_id,
        order_id: orderId,
        ip_address: clientIP,
        user_agent: userAgent,
        success: false,
        error_message: 'Order not found or access denied',
        timestamp: new Date().toISOString()
      })
      
      return new Response(
        JSON.stringify({ error: 'Order not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Verify user owns the order (unless admin)
    if (order.user_id !== user.id && profile.role !== 'admin') {
      await logSecurityEvent(supabase, {
        event_type: 'unauthorized_access',
        user_id: user.id,
        tenant_id: profile.tenant_id,
        order_id: orderId,
        ip_address: clientIP,
        user_agent: userAgent,
        success: false,
        error_message: 'Order ownership verification failed',
        metadata: { order_owner: order.user_id, requesting_user: user.id },
        timestamp: new Date().toISOString()
      })
      
      return new Response(
        JSON.stringify({ error: 'Access denied - Order ownership verification failed' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Only allow access to digital codes for completed orders
    if (order.status !== 'completed') {
      await logSecurityEvent(supabase, {
        event_type: 'unauthorized_access',
        user_id: user.id,
        tenant_id: profile.tenant_id,
        order_id: orderId,
        ip_address: clientIP,
        user_agent: userAgent,
        success: false,
        error_message: 'Order not completed',
        metadata: { order_status: order.status },
        timestamp: new Date().toISOString()
      })
      
      return new Response(
        JSON.stringify({ error: 'Digital codes only available for completed orders' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get digital codes with enhanced security
    const { data: codes, error: codesError } = await supabase
      .from('digital_codes')
      .select(`
        id,
        key_encrypted,
        used,
        viewed_count,
        last_viewed_at,
        assigned_at
      `)
      .eq('assigned_to_order_id', orderId)
      .eq('tenant_id', profile.tenant_id)
      .limit(MAX_CODES_PER_REQUEST)

    if (codesError) {
      await logSecurityEvent(supabase, {
        event_type: 'digital_code_access',
        user_id: user.id,
        tenant_id: profile.tenant_id,
        order_id: orderId,
        ip_address: clientIP,
        user_agent: userAgent,
        success: false,
        error_message: 'Database error fetching codes',
        timestamp: new Date().toISOString()
      })
      
      return new Response(
        JSON.stringify({ error: 'Failed to fetch digital codes' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Decrypt codes for response
    const decryptedCodes = await Promise.all(
      (codes || []).map(async (code) => ({
        ...code,
        key: await decryptDigitalCode(code.key_encrypted)
      }))
    )

    // Update viewed count and log access
    if (codes && codes.length > 0) {
      const codeIds = codes.map(code => code.id)
      
      // Update view tracking
      await supabase
        .from('digital_codes')
        .update({ 
          viewed_count: supabase.raw('viewed_count + 1'),
          last_viewed_at: new Date().toISOString()
        })
        .in('id', codeIds)
    }

    // Log successful access
    await logSecurityEvent(supabase, {
      event_type: 'digital_code_access',
      user_id: user.id,
      tenant_id: profile.tenant_id,
      order_id: orderId,
      ip_address: clientIP,
      user_agent: userAgent,
      success: true,
      metadata: { 
        codes_count: codes?.length || 0,
        user_role: profile.role
      },
      timestamp: new Date().toISOString()
    })

    return new Response(
      JSON.stringify({
        success: true,
        codes: decryptedCodes,
        metadata: {
          order_id: orderId,
          codes_count: decryptedCodes.length,
          access_time: new Date().toISOString()
        }
      }),
      { 
        status: 200, 
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json',
          'X-RateLimit-Remaining': String(rateLimit.remaining)
        } 
      }
    )

  } catch (error) {
    console.error('Unexpected error in secure-digital-codes function:', error)
    
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
