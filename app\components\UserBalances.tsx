'use client'

import { useData } from '../contexts/DataContext'
import { Wallet } from 'lucide-react'
import { getCurrencySymbol } from '../utils/currency'

export function UserBalances() {
  const { currencies, selectedCurrency, userBalances, formatPrice } = useData()

  const selectedBalance = userBalances[selectedCurrency] || 0
  const selectedCurrencyData = currencies.find(c => c.code === selectedCurrency)

  return (
    <div className="bg-gradient-to-br from-purple-600 to-blue-600 rounded-xl p-6 text-white">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2 space-x-reverse">
          <Wallet className="w-6 h-6" />
          <span className="text-lg font-semibold">رصيد المحفظة</span>
        </div>
      </div>
      
      <div className="text-3xl font-bold mb-4">
        {selectedCurrencyData ? (
          `${selectedBalance.toFixed(2)} ${getCurrencySymbol(selectedCurrencyData.code)}`
        ) : (
          `${selectedBalance.toFixed(2)} ${getCurrencySymbol(selectedCurrency)}`
        )}
      </div>

      {Object.keys(userBalances).length > 1 && (
        <div className="space-y-2">
          <p className="text-sm text-white/80">جميع الأرصدة:</p>
          {Object.entries(userBalances).map(([currencyCode, balance]) => {
            const currency = currencies.find(c => c.code === currencyCode)
            return (
              <div key={currencyCode} className="flex justify-between text-sm">
                <span>{currency?.name || currencyCode}</span>
                <span>{balance.toFixed(2)} {getCurrencySymbol(currencyCode)}</span>
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}
