-- Add inventory management fields to packages table
-- This migration adds support for manual inventory tracking alongside digital codes

-- Add new columns to packages table
ALTER TABLE packages 
ADD COLUMN IF NOT EXISTS manual_quantity INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS track_inventory BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS unlimited_stock BOOLEAN DEFAULT false;

-- Add comments for documentation
COMMENT ON COLUMN packages.manual_quantity IS 'Manual inventory quantity for non-digital packages';
COMMENT ON COLUMN packages.track_inventory IS 'Whether to track inventory for this package (true for manual tracking, false for digital codes only)';
COMMENT ON COLUMN packages.unlimited_stock IS 'Whether this package has unlimited stock (overrides quantity checks)';

-- Create index for performance on inventory queries
CREATE INDEX IF NOT EXISTS idx_packages_inventory 
ON packages (tenant_id, track_inventory, manual_quantity) 
WHERE track_inventory = true;

-- Update existing packages to have sensible defaults
-- Packages with digital codes should not track manual inventory by default
UPDATE packages 
SET track_inventory = false, 
    unlimited_stock = false,
    manual_quantity = 0
WHERE has_digital_codes = true;

-- Packages without digital codes should track manual inventory and start with unlimited stock
UPDATE packages 
SET track_inventory = true, 
    unlimited_stock = true,
    manual_quantity = 999999
WHERE has_digital_codes = false OR has_digital_codes IS NULL;

-- Add constraint to ensure manual_quantity is not negative
ALTER TABLE packages 
ADD CONSTRAINT check_manual_quantity_non_negative 
CHECK (manual_quantity >= 0);

-- Update RLS policies to include new fields (they should already be covered by existing tenant_id policies)
-- No additional RLS policies needed as the existing tenant_id policies will cover these new fields

-- Create a function to calculate available inventory for a package
CREATE OR REPLACE FUNCTION get_package_available_quantity(package_id UUID)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    pkg_record RECORD;
    digital_codes_count INTEGER;
    available_quantity INTEGER;
BEGIN
    -- Get package details
    SELECT 
        manual_quantity,
        track_inventory,
        unlimited_stock,
        has_digital_codes
    INTO pkg_record
    FROM packages 
    WHERE id = package_id;
    
    -- If package not found, return 0
    IF NOT FOUND THEN
        RETURN 0;
    END IF;
    
    -- If unlimited stock, return a large number
    IF pkg_record.unlimited_stock THEN
        RETURN 999999;
    END IF;
    
    -- If package uses digital codes, count available codes
    IF pkg_record.has_digital_codes THEN
        SELECT COUNT(*)
        INTO digital_codes_count
        FROM digital_codes
        WHERE package_id = get_package_available_quantity.package_id
        AND used = false
        AND assigned_to_order_id IS NULL;
        
        RETURN COALESCE(digital_codes_count, 0);
    END IF;
    
    -- If package tracks manual inventory, return manual quantity
    IF pkg_record.track_inventory THEN
        RETURN COALESCE(pkg_record.manual_quantity, 0);
    END IF;
    
    -- Default case: return 0 (no inventory tracking)
    RETURN 0;
END;
$$;

-- Create a function to reduce inventory when an order is placed
CREATE OR REPLACE FUNCTION reduce_package_inventory(package_id UUID, quantity INTEGER)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    pkg_record RECORD;
    available_qty INTEGER;
BEGIN
    -- Get package details
    SELECT 
        manual_quantity,
        track_inventory,
        unlimited_stock,
        has_digital_codes
    INTO pkg_record
    FROM packages 
    WHERE id = package_id;
    
    -- If package not found, return false
    IF NOT FOUND THEN
        RETURN false;
    END IF;
    
    -- If unlimited stock, always return true (no reduction needed)
    IF pkg_record.unlimited_stock THEN
        RETURN true;
    END IF;
    
    -- If package uses digital codes, inventory is managed by code assignment
    -- This function doesn't handle digital codes (handled separately)
    IF pkg_record.has_digital_codes THEN
        RETURN true;
    END IF;
    
    -- If package tracks manual inventory, reduce the quantity
    IF pkg_record.track_inventory THEN
        -- Check if we have enough inventory
        IF pkg_record.manual_quantity >= quantity THEN
            UPDATE packages 
            SET manual_quantity = manual_quantity - quantity
            WHERE id = package_id;
            RETURN true;
        ELSE
            -- Not enough inventory
            RETURN false;
        END IF;
    END IF;
    
    -- Default case: no inventory tracking, always allow
    RETURN true;
END;
$$;

-- Grant execute permissions on the functions to authenticated users
GRANT EXECUTE ON FUNCTION get_package_available_quantity(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION reduce_package_inventory(UUID, INTEGER) TO authenticated;
